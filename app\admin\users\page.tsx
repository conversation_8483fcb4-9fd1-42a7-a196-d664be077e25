"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Search, Plus, MoreHorizontal, Download, AlertCircle, Loader2 } from "lucide-react"
import { adminApi } from "@/lib/admin-api"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { AddUserForm } from "@/components/add-user-form"
import { toast } from "@/hooks/use-toast"

interface User {
  id: string
  studentId: string
  name: string
  email: string
  phone: string
  role: "admin" | "customer" | "staff"
  status: "active" | "inactive" | "pending" | "suspended"
  loans: number
  joinDate: string
  lastLogin: string | null
  faceImage?: string
  profileImage?: string
  isEmailVerified?: boolean
  isPhoneVerified?: boolean
  isFaceVerified?: boolean
  passwordChanged?: boolean
}

export default function UsersPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [isAddUserOpen, setIsAddUserOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [users, setUsers] = useState<User[]>([
    {
      id: "USR-001",
      studentId: "STU-2023-001",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+1234567890",
      role: "customer",
      status: "active",
      loans: 2,
      joinDate: "2025-01-15",
      lastLogin: "2025-03-14T09:30:00",
    },
    {
      id: "USR-002",
      studentId: "STU-2023-002",
      name: "Sarah Smith",
      email: "<EMAIL>",
      phone: "+1987654321",
      role: "customer",
      status: "active",
      loans: 1,
      joinDate: "2025-02-03",
      lastLogin: "2025-03-12T14:45:00",
    },
    {
      id: "USR-003",
      studentId: "STU-2023-003",
      name: "Michael Johnson",
      email: "<EMAIL>",
      phone: "+1122334455",
      role: "customer",
      status: "inactive",
      loans: 0,
      joinDate: "2024-11-22",
      lastLogin: "2025-01-05T11:20:00",
    },
    {
      id: "USR-004",
      studentId: "STU-2023-004",
      name: "Emily Brown",
      email: "<EMAIL>",
      phone: "+1555666777",
      role: "customer",
      status: "pending",
      loans: 0,
      joinDate: "2025-03-10",
      lastLogin: null,
    },
    {
      id: "USR-005",
      studentId: "STU-2023-005",
      name: "David Wilson",
      email: "<EMAIL>",
      phone: "+1777888999",
      role: "customer",
      status: "active",
      loans: 3,
      joinDate: "2024-09-05",
      lastLogin: "2025-03-15T16:10:00",
    },
  ])

  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await adminApi.getUsers(currentPage, 10, searchQuery);

        if (response.success) {
          setUsers(response.data.users);
          setTotalPages(response.data.pagination.pages);
        } else {
          setError('Failed to fetch users');
          // Fallback to mock data if API fails
          console.warn('Using mock data due to API failure');
        }
      } catch (err) {
        console.error('Error fetching users:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching users');
        // Keep the mock data as fallback
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [currentPage, searchQuery]);

  // Filter users locally (this is a backup if API filtering doesn't work)
  const filteredUsers = users.filter(
    (user) =>
      user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.studentId?.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleAddUser = async (userData: any) => {
    try {
      // Format the data for the API
      const apiUserData = {
        studentId: userData.studentId,
        name: `${userData.firstName} ${userData.lastName}`,
        email: userData.email,
        phone: userData.phone,
        status: userData.status,
        role: userData.role || 'customer',
        faceImage: userData.faceImage,
      };

      // Call the API to create the user
      const response = await adminApi.createUser(apiUserData);

      if (response.success) {
        // Add the new user to the state
        setUsers([...users, response.data]);
        setIsAddUserOpen(false);
        toast({
          title: "User Created",
          description: `${response.data.name} has been added successfully.`,
        });

        // Refresh the user list
        const refreshResponse = await adminApi.getUsers(currentPage, 10, searchQuery);
        if (refreshResponse.success) {
          setUsers(refreshResponse.data.users);
          setTotalPages(refreshResponse.data.pagination.pages);
        }
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create user",
          variant: "destructive",
        });
      }
    } catch (err) {
      console.error('Error creating user:', err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "An error occurred while creating the user",
        variant: "destructive",
      });

      // Fallback to local creation if API fails
      const newUser: User = {
        id: `USR-${String(users.length + 1).padStart(3, "0")}`,
        studentId: userData.studentId,
        name: `${userData.firstName} ${userData.lastName}`,
        email: userData.email,
        phone: userData.phone,
        status: userData.status,
        role: userData.role || 'customer',
        loans: 0,
        joinDate: new Date().toISOString().split("T")[0],
        lastLogin: null,
        faceImage: userData.faceImage,
      };

      setUsers([...users, newUser]);
      setIsAddUserOpen(false);
      toast({
        title: "User Created (Offline Mode)",
        description: `${newUser.name} has been added successfully in offline mode.`,
      });
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "inactive":
        return (
          <Badge variant="outline" className="text-gray-500">
            Inactive
          </Badge>
        )
      case "pending":
        return <Badge className="bg-amber-500">Pending</Badge>
      case "suspended":
        return <Badge className="bg-red-500">Suspended</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // Add function to handle user status change
  const handleStatusChange = async (userId: string, newStatus: string) => {
    try {
      const response = await adminApi.updateUserStatus(userId, newStatus);

      if (response.success) {
        // Update the user in the state
        setUsers(users.map(user =>
          user.id === userId ? { ...user, status: newStatus as any } : user
        ));

        toast({
          title: "Status Updated",
          description: `User status has been updated to ${newStatus}.`,
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update user status",
          variant: "destructive",
        });
      }
    } catch (err) {
      console.error('Error updating user status:', err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "An error occurred while updating the user status",
        variant: "destructive",
      });
    }
  };

  // Add function to handle user deletion
  const handleDeleteUser = async (userId: string) => {
    if (!confirm("Are you sure you want to delete this user? This action cannot be undone.")) {
      return;
    }

    try {
      const response = await adminApi.deleteUser(userId);

      if (response.success) {
        // Remove the user from the state
        setUsers(users.filter(user => user.id !== userId));

        toast({
          title: "User Deleted",
          description: `User has been deleted successfully.`,
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete user",
          variant: "destructive",
        });
      }
    } catch (err) {
      console.error('Error deleting user:', err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "An error occurred while deleting the user",
        variant: "destructive",
      });
    }
  };

  // Add function to handle password reset
  const handleResetPassword = async (userId: string) => {
    try {
      const response = await adminApi.resetUserPassword(userId);

      if (response.success) {
        toast({
          title: "Password Reset",
          description: `User password has been reset to: ${response.data.password}`,
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to reset user password",
          variant: "destructive",
        });
      }
    } catch (err) {
      console.error('Error resetting user password:', err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "An error occurred while resetting the user password",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Users</h1>
        <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setIsAddUserOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
          <CardDescription>View and manage all users in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search users..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <Button variant="outline" disabled={isLoading || filteredUsers.length === 0}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>

          {isLoading && (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading users...</span>
            </div>
          )}

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Student ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Loans</TableHead>
                <TableHead>Join Date</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.id}</TableCell>
                    <TableCell>{user.studentId}</TableCell>
                    <TableCell>{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{user.phone}</TableCell>
                    <TableCell>{user.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'Customer'}</TableCell>
                    <TableCell>{getStatusBadge(user.status)}</TableCell>
                    <TableCell>{user.loans}</TableCell>
                    <TableCell>{new Date(user.joinDate).toLocaleDateString()}</TableCell>
                    <TableCell>{user.lastLogin ? new Date(user.lastLogin).toLocaleString() : "Never"}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Edit User</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>Reset Password</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {user.status === "active" && (
                            <DropdownMenuItem onClick={() => handleStatusChange(user.id, "inactive")} className="text-amber-600">
                              Deactivate
                            </DropdownMenuItem>
                          )}
                          {user.status === "inactive" && (
                            <DropdownMenuItem onClick={() => handleStatusChange(user.id, "active")} className="text-green-600">
                              Activate
                            </DropdownMenuItem>
                          )}
                          {user.status === "pending" && (
                            <DropdownMenuItem onClick={() => handleStatusChange(user.id, "active")} className="text-green-600">
                              Approve
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem onClick={() => handleStatusChange(user.id, "suspended")} className="text-red-600">
                            Suspend
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleDeleteUser(user.id)} className="text-red-600">
                            Delete User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={11} className="text-center py-6 text-muted-foreground">
                    No users found matching your search criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          {!isLoading && filteredUsers.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No users found</p>
            </div>
          )}

          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1 || isLoading}
                >
                  Previous
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      disabled={isLoading}
                    >
                      {page}
                    </Button>
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages || isLoading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <AddUserForm isOpen={isAddUserOpen} onClose={() => setIsAddUserOpen(false)} onSubmit={handleAddUser} />
    </div>
  )
}

