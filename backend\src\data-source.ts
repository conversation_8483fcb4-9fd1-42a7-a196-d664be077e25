import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { User } from './models/User';
import { Loan } from './models/Loan';
import { Transaction } from './models/Transaction';
import { Notification } from './models/Notification';
import { Otp } from './models/Otp';
import { Document } from './models/Document';
import dotenv from 'dotenv';

dotenv.config();

const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';
// When running compiled code, always use compiled paths regardless of NODE_ENV
// This is determined by checking if we're running from a compiled file
const isRunningFromDist = __filename.includes('dist');
const useCompiledPaths = isProduction || isRunningFromDist;



export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'loan_app',
  synchronize: isDevelopment,
  logging: isDevelopment,
  entities: [User, Loan, Transaction, Notification, Otp, Document],
  migrations: useCompiledPaths ? ['dist/migrations/**/*.js'] : ['src/migrations/**/*.ts'],
  subscribers: useCompiledPaths ? ['dist/subscribers/**/*.js'] : ['src/subscribers/**/*.ts'],
});