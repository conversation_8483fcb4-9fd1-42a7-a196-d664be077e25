const express = require('express');
const next = require('next');
const path = require('path');

console.log('Starting Umlamleli Loan Management Application...');

// Check if we're running in a packaged environment
const isPackaged = process.pkg !== undefined;
const dev = !isPackaged && process.env.NODE_ENV !== 'production';

async function startApplication() {
  try {
    // Set environment variables
    process.env.NODE_ENV = 'production';

    console.log('Starting backend server...');

    // Start backend server by requiring it
    const backendPath = path.join(__dirname, 'backend', 'dist', 'server.js');
    require(backendPath);

    console.log('Backend server started on port 3001');

    // Wait for backend to initialize
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('Starting frontend server...');

    // Initialize Next.js
    const app = next({
      dev: false,
      dir: __dirname,
      port: 3000
    });

    const handle = app.getRequestHandler();

    await app.prepare();

    // Create Express server for frontend
    const server = express();

    // Handle all requests with Next.js
    server.all('*', (req, res) => {
      return handle(req, res);
    });

    // Start frontend server
    server.listen(3000, (err) => {
      if (err) throw err;
      console.log('✅ Frontend server ready on http://localhost:3000');
      console.log('✅ Backend API ready on http://localhost:3001');
      console.log('🚀 Application is ready!');
    });

  } catch (error) {
    console.error('Failed to start application:', error);
    process.exit(1);
  }
}

// Start the application
startApplication();
