"use client"

import React, { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { ArrowLeft, Download, Eye, Loader2, ArrowUpDown } from "lucide-react"
import { loanApi } from "@/lib/api"
import { toast } from "sonner"
import { LoanFilters, LoanFilters as LoanFiltersType } from "@/components/loan/LoanFilters"
import { PaymentTimeline } from "@/components/loan/PaymentTimeline"
import { HealthIndicator, HealthScoreDetails } from "@/components/loan/HealthIndicator"

interface Loan {
  id: string;
  amount: number;
  purpose: string;
  status: string;
  createdAt: string;
  dueDate: string;
  interestRate: number;
  termInMonths: number;
  collateral?: string;
  amountPaid?: number;
  disbursedAt?: string;
  healthScore?: number;
  healthDetails?: {
    components?: {
      paymentTimeliness: number;
      amountPaid: number;
      timeToMaturity: number;
    };
    status: string;
    details: string;
  };
}

export default function LoanHistoryPage() {
  const [loans, setLoans] = useState<Loan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedRowId, setExpandedRowId] = useState<string | null>(null);
  const [filters, setFilters] = useState<LoanFiltersType>({});
  const [sortField, setSortField] = useState<string>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  const fetchLoans = useCallback(async (filterParams?: LoanFiltersType) => {
    try {
      setIsLoading(true);
      const response = await loanApi.getUserLoans(filterParams);
      if (response.success) {
        console.log("Fetched loans:", response.data);
        const loansData = response.data || [];

        // Process loans to ensure correct interest calculation
        const processedLoans = loansData.map((loan: Loan) => {
          // Log loan data for debugging
          console.log(`Processing loan ${loan.id}:`, {
            amount: loan.amount,
            interestRate: loan.interestRate,
            status: loan.status
          });

          return loan;
        });

        setLoans(processedLoans);

        // Fetch health scores for each loan
        if (loansData.length > 0) {
          fetchHealthScores(loansData);
        }
      } else {
        console.error("Failed to fetch loans:", response.message);
        setError(response.message || "Failed to load your loans");
        toast.error("Could not load loans", {
          description: response.message || "Please try again later"
        });
      }
    } catch (err: any) {
      console.error("Error fetching loans:", err);
      setError(err.message || "An error occurred while loading your loans");
      toast.error("Error loading loans", {
        description: err.message || "Please try refreshing the page"
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch health scores for loans
  const fetchHealthScores = async (loansData: Loan[]) => {
    try {
      // Only fetch health scores for loans that are not pending or rejected
      const activeLoans = loansData.filter(loan =>
        loan.status !== 'pending' && loan.status !== 'rejected'
      );

      if (activeLoans.length === 0) return;

      // Create a copy of the loans array to update
      const updatedLoans = [...loansData];

      // Fetch health scores for each active loan
      for (const loan of activeLoans) {
        try {
          const response = await loanApi.getLoanHealthScore(loan.id);
          if (response.success && response.data) {
            // Find the loan in our array and update it
            const loanIndex = updatedLoans.findIndex(l => l.id === loan.id);
            if (loanIndex !== -1) {
              updatedLoans[loanIndex] = {
                ...updatedLoans[loanIndex],
                healthScore: response.data.score,
                healthDetails: response.data
              };
            }
          }
        } catch (error) {
          console.error(`Error fetching health score for loan ${loan.id}:`, error);
          // Continue with other loans even if one fails
        }
      }

      // Update the state with the new loan data
      setLoans(updatedLoans);
    } catch (error) {
      console.error("Error fetching health scores:", error);
      // Don't set an error state here, as we still want to display the loans
    }
  };

  // Initial fetch on component mount
  useEffect(() => {
    fetchLoans();
  }, [fetchLoans]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: LoanFiltersType) => {
    setFilters(newFilters);
    fetchLoans(newFilters);
  }, [fetchLoans]);

  // Handle sorting
  const handleSort = (field: string) => {
    const newDirection = field === sortField && sortDirection === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortDirection(newDirection);

    // Sort the loans array
    const sortedLoans = [...loans].sort((a, b) => {
      let aValue = a[field as keyof Loan];
      let bValue = b[field as keyof Loan];

      // Handle date fields
      if (field === "createdAt" || field === "dueDate") {
        aValue = new Date(aValue as string).getTime();
        bValue = new Date(bValue as string).getTime();
      }

      // Handle numeric fields
      if (typeof aValue === "number" && typeof bValue === "number") {
        return newDirection === "asc" ? aValue - bValue : bValue - aValue;
      }

      // Handle string fields
      if (typeof aValue === "string" && typeof bValue === "string") {
        return newDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return 0;
    });

    setLoans(sortedLoans);
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return <Badge className="bg-green-500">Approved</Badge>
      case "disbursed":
        return <Badge className="bg-blue-500">Disbursed</Badge>
      case "paid":
      case "completed":
        return <Badge className="bg-green-500">Paid</Badge>
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>
      case "rejected":
        return <Badge className="bg-red-500">Rejected</Badge>
      case "overdue":
        return <Badge className="bg-red-500">Overdue</Badge>
      default:
        return <Badge>{status.charAt(0).toUpperCase() + status.slice(1)}</Badge>
    }
  }

  // Format currency values with Emalangeni symbol and exactly two decimal places
  const formatCurrency = (amount: any): string => {
    // Check if amount is a valid number
    if (amount === null || amount === undefined) {
      return 'E0.00';
    }

    // Convert to number if it's a string or other type
    const numericAmount = typeof amount === 'number' ? amount : Number(amount);

    // Check if conversion resulted in a valid number
    if (isNaN(numericAmount)) {
      console.warn('Failed to convert amount to number:', amount);
      return 'E0.00';
    }

    // Format with 2 decimal places
    return `E${numericAmount.toFixed(2)}`;
  }

  const formatDate = (dateString: string | undefined | null) => {
    try {
      // Handle null or undefined
      if (!dateString) {
        return "N/A";
      }

      // Parse the date
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date:', dateString);
        return "N/A";
      }

      // Check for Unix epoch dates (1970)
      if (date.getFullYear() <= 1970) {
        console.warn('Detected Unix epoch date:', dateString);
        return "N/A";
      }

      // Format as DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error('Error formatting date:', e);
      return "N/A";
    }
  }

  const handleViewDetails = (loanId: string) => {
    // In a real app, this would navigate to a loan details page
    console.log("Viewing loan details:", loanId);
    toast.info("Feature coming soon", {
      description: "Loan details view is under development"
    });
  }

  const handleDownloadStatement = (loanId: string) => {
    // In a real app, this would download a loan statement
    console.log("Downloading statement for loan:", loanId);
    toast.info("Feature coming soon", {
      description: "Statement download is under development"
    });
  }

  const toggleExpandRow = (loanId: string) => {
    if (expandedRowId === loanId) {
      setExpandedRowId(null);
    } else {
      setExpandedRowId(loanId);
    }
  }

  // Helper function to calculate total repayment amount
  const calculateTotalRepayment = (principal: any, interestRate: any) => {
    // Ensure we're using numeric values for the calculation
    const numericPrincipal = typeof principal === 'number' ? principal : Number(principal);
    const numericRate = typeof interestRate === 'number' ? interestRate : Number(interestRate);

    // Validate inputs
    if (isNaN(numericPrincipal) || isNaN(numericRate) || numericRate <= 0) {
      console.warn('Invalid inputs for total payment calculation:', { principal, interestRate });
      return 0;
    }

    // Calculate interest amount (principal × interest rate percentage)
    const interestAmount = (numericPrincipal * numericRate) / 100;

    // Calculate total payment (principal + interest)
    const totalPayment = numericPrincipal + interestAmount;

    // Log calculation for debugging
    console.log('Total payment calculation:', {
      principal: numericPrincipal,
      rate: numericRate,
      interestAmount,
      totalPayment
    });

    return totalPayment;
  }

  return (
    <div className="min-h-screen bg-blue-50 py-8 px-4 overflow-auto">
      <div className="container mx-auto max-w-5xl">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-blue-900">Loan History</h1>
          <Button asChild variant="outline">
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Your Loans</CardTitle>
            <CardDescription>View all your past and current loans</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Loan Filters */}
            <LoanFilters
              onFilterChange={handleFilterChange}
              className="mb-6"
            />

            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                <span className="ml-2 text-lg text-blue-500">Loading your loans...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-500">
                <p>{error}</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </div>
            ) : loans.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No loans match your filter criteria.</p>
                <div className="flex justify-center gap-4 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => handleFilterChange({})}
                  >
                    Clear Filters
                  </Button>
                  <Button asChild className="bg-blue-600 hover:bg-blue-700">
                    <Link href="/loan/apply">Apply for a Loan</Link>
                  </Button>
                </div>
              </div>
            ) : (
            <div className="overflow-x-auto -mx-4 sm:mx-0">
              <Table className="min-w-[800px] sm:min-w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">Loan ID</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("amount")}
                    >
                      <div className="flex items-center">
                        Principal
                        {sortField === "amount" && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("interestRate")}
                    >
                      <div className="flex items-center">
                        Interest Rate
                        {sortField === "interestRate" && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Total Repayment</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("purpose")}
                    >
                      <div className="flex items-center">
                        Purpose
                        {sortField === "purpose" && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Collateral</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("createdAt")}
                    >
                      <div className="flex items-center">
                        Application Date
                        {sortField === "createdAt" && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("dueDate")}
                    >
                      <div className="flex items-center">
                        Due Date
                        {sortField === "dueDate" && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("status")}
                    >
                      <div className="flex items-center">
                        Status
                        {sortField === "status" && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("healthScore")}
                    >
                      <div className="flex items-center">
                        Health
                        {sortField === "healthScore" && (
                          <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loans.map((loan) => (
                    <React.Fragment key={loan.id}>
                      <TableRow
                        className="cursor-pointer"
                        onClick={() => toggleExpandRow(loan.id)}
                      >
                        <TableCell className="font-medium whitespace-nowrap">{loan.id.substring(0, 8)}...</TableCell>
                        <TableCell>{formatCurrency(loan.amount)}</TableCell>
                        <TableCell>{loan.interestRate ? `${loan.interestRate}%` : 'N/A'}</TableCell>
                        <TableCell>
                          {(() => {
                            // Always ensure we have valid numeric values
                            const amount = typeof loan.amount === 'number' ? loan.amount : Number(loan.amount);
                            const rate = typeof loan.interestRate === 'number' ? loan.interestRate : Number(loan.interestRate);

                            // Check if we have valid numbers after conversion
                            if (isNaN(amount) || isNaN(rate) || rate <= 0) {
                              console.warn(`Invalid loan data for interest calculation: amount=${loan.amount}, rate=${loan.interestRate}`);
                              return 'N/A';
                            }

                            // Calculate the correct total payment directly in the render
                            const interestAmount = (amount * rate) / 100;
                            const calculatedTotal = amount + interestAmount;

                            // Log the calculation for debugging
                            console.log(`Loan ${loan.id} total payment calculation:`, {
                              principal: amount,
                              rate: rate,
                              interestAmount: interestAmount,
                              totalPayment: calculatedTotal
                            });

                            return formatCurrency(calculatedTotal);
                          })()}
                        </TableCell>
                        <TableCell>{loan.purpose}</TableCell>
                        <TableCell>{loan.collateral || 'None'}</TableCell>
                        <TableCell>{formatDate(loan.createdAt)}</TableCell>
                        <TableCell>{formatDate(loan.dueDate)}</TableCell>
                        <TableCell>{getStatusBadge(loan.status)}</TableCell>
                        <TableCell>
                          {loan.healthScore && loan.status !== 'pending' && loan.status !== 'rejected' ? (
                            <HealthIndicator score={loan.healthScore} size="sm" />
                          ) : (
                            <span className="text-gray-400 text-xs">N/A</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button size="icon" variant="ghost" onClick={(e) => {
                              e.stopPropagation();
                              handleViewDetails(loan.id);
                            }}>
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View details</span>
                            </Button>
                            <Button size="icon" variant="ghost" onClick={(e) => {
                              e.stopPropagation();
                              handleDownloadStatement(loan.id);
                            }}>
                              <Download className="h-4 w-4" />
                              <span className="sr-only">Download statement</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>

                      {/* Expandable row for mobile */}
                      {expandedRowId === loan.id && (
                        <TableRow className="bg-blue-50">
                          <TableCell colSpan={10} className="p-4">
                            <div className="space-y-6">
                              {/* Loan details grid */}
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                  <p className="font-semibold">Loan ID:</p>
                                  <p className="text-gray-700">{loan.id}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Principal:</p>
                                  <p className="text-gray-700">{formatCurrency(loan.amount)}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Interest Rate:</p>
                                  <p className="text-gray-700">{loan.interestRate ? `${loan.interestRate}%` : 'N/A'}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Total Repayment:</p>
                                  <p className="text-gray-700">
                                    {(() => {
                                      // Always ensure we have valid numeric values
                                      const amount = typeof loan.amount === 'number' ? loan.amount : Number(loan.amount);
                                      const rate = typeof loan.interestRate === 'number' ? loan.interestRate : Number(loan.interestRate);

                                      // Check if we have valid numbers after conversion
                                      if (isNaN(amount) || isNaN(rate) || rate <= 0) {
                                        return 'N/A';
                                      }

                                      // Calculate the correct total payment directly in the render
                                      const interestAmount = (amount * rate) / 100;
                                      const calculatedTotal = amount + interestAmount;

                                      return formatCurrency(calculatedTotal);
                                    })()}
                                  </p>
                                </div>
                                <div>
                                  <p className="font-semibold">Purpose:</p>
                                  <p className="text-gray-700">{loan.purpose}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Collateral:</p>
                                  <p className="text-gray-700">{loan.collateral || 'None provided'}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Application Date:</p>
                                  <p className="text-gray-700">{formatDate(loan.createdAt)}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Due Date:</p>
                                  <p className="text-gray-700">{formatDate(loan.dueDate)}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Status:</p>
                                  <p className="text-gray-700">{loan.status.charAt(0).toUpperCase() + loan.status.slice(1)}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Term:</p>
                                  <p className="text-gray-700">{loan.termInMonths} {loan.termInMonths === 1 ? 'month' : 'months'}</p>
                                </div>
                              </div>

                              {/* Payment Timeline */}
                              {loan.status !== 'pending' && loan.status !== 'rejected' && (
                                <div className="border-t pt-4">
                                  <PaymentTimeline loanId={loan.id} />
                                </div>
                              )}

                              {/* Health Score Details */}
                              {loan.healthScore && loan.healthDetails && loan.status !== 'pending' && loan.status !== 'rejected' && (
                                <div className="border-t pt-4">
                                  <HealthScoreDetails
                                    score={loan.healthScore}
                                    details={loan.healthDetails}
                                  />
                                </div>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

