import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createLogger, format, transports } from 'winston';
import 'reflect-metadata';
// @ts-ignore
import helmet from 'helmet';
// @ts-ignore
import morgan from 'morgan';
// @ts-ignore
import rateLimit from 'express-rate-limit';
import authRoutes from './routes/auth.routes';
import loanRoutes from './routes/loan.routes';
import transactionRoutes from './routes/transaction.routes';
import healthRoutes from './routes/health.routes';
import userRoutes from './routes/user.routes';
import profileRoutes from './routes/profile.routes';
import notificationRoutes from './routes/notification.routes';
import otpRoutes from './routes/otp.routes';
import documentRoutes from './routes/document.routes';
import reportsRoutes from './routes/reports.routes';
import dashboardRoutes from './routes/dashboard.routes';

// Load environment variables
dotenv.config();

// Initialize express app
const app = express();

// Initialize logger
const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  transports: [
    new transports.File({ filename: 'logs/error.log', level: 'error' }),
    new transports.File({ filename: 'logs/combined.log' }),
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ]
});

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true }));
app.use(morgan('dev')); // Request logging

// We'll use base64 for images instead of file uploads

// Rate limiting - More flexible configuration
const generalLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '15') * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '500'), // Increased from 100 to 500 for normal operation
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Skip rate limiting for health checks and certain endpoints
  skip: (req) => {
    // Skip rate limiting for health endpoint
    if (req.path === '/api/health') {
      return true;
    }
    return false;
  }
});

// Strict rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Limit auth attempts to 20 per 15 minutes per IP
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply general rate limiting to all routes
app.use(generalLimiter);

// Routes
console.log('Registering API routes:');
console.log('- /api/auth (with strict rate limiting)');
app.use('/api/auth', authLimiter, authRoutes);
console.log('- /api/loans');
app.use('/api/loans', loanRoutes);
console.log('- /api/transactions');
app.use('/api/transactions', transactionRoutes);
console.log('- /api/health');
app.use('/api/health', healthRoutes);
console.log('- /api/admin/users');
app.use('/api/admin/users', userRoutes);
console.log('- /api/profile');
app.use('/api/profile', profileRoutes);
console.log('- /api/notifications');
app.use('/api/notifications', notificationRoutes);
console.log('- /api/otp');
app.use('/api/otp', otpRoutes);
console.log('- /api/documents');
app.use('/api/documents', documentRoutes);
console.log('- /api/reports');
app.use('/api/reports', reportsRoutes);
console.log('- /api/admin/dashboard');
app.use('/api/admin/dashboard', dashboardRoutes);
console.log('All routes registered');

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
  });
});

// Routes will be added here
// app.use('/api/users', userRoutes);
// app.use('/api/admin', adminRoutes);
// app.use('/api/payments', paymentRoutes);

export { app };