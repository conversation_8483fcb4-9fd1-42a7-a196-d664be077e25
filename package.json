{"name": "um<PERSON>leli-loan-management", "version": "1.0.0", "description": "Umlamleli Loan Management Web Application", "main": "launcher.js", "bin": "launcher.js", "private": true, "scripts": {"copy-wasm": "node scripts/copy-wasm-files.js", "dev": "npm run copy-wasm && next dev", "build": "npm run copy-wasm && next build", "start": "next start", "lint": "next lint", "prebuild": "npm run copy-wasm", "package": "pkg .", "build-all": "npm run build && cd backend && npm run build && cd .."}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@tensorflow/tfjs": "^4.22.0", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "face-api.js": "^0.22.2", "framer-motion": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "^15.2.3", "next-themes": "^0.4.4", "onnxruntime-web": "^1.21.0", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "copy-files-from-to": "^3.12.1", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}, "pkg": {"scripts": ["backend/dist/**/*.js", ".next/server/**/*.js", ".next/static/**/*.js"], "assets": ["backend/dist/**/*", ".next/**/*", "public/**/*", "backend/.env"], "targets": ["node18-win-x64", "node18-linux-x64", "node18-macos-x64"]}}