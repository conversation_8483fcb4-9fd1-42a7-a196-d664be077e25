import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

export const hashPassword = async (password: string): Promise<string> => {
  console.log('Hashing password...');
  console.log('Input password:', password);

  try {
    const salt = await bcrypt.genSalt(10);
    console.log('Generated salt:', salt);

    const hashedPassword = await bcrypt.hash(password, salt);
    console.log('Password hashed successfully. Hash length:', hashedPassword.length);
    console.log('Hash:', hashedPassword);

    return hashedPassword;
  } catch (error) {
    console.error('Error hashing password:', error);
    throw error;
  }
};

export const comparePasswords = async (
  password: string,
  hashedPassword: string
): Promise<boolean> => {
  console.log('Comparing passwords in auth util');
  console.log('Raw password length:', password.length);
  console.log('Raw password:', password);
  console.log('Hashed password length:', hashedPassword.length);
  console.log('Hashed password:', hashedPassword);

  try {
    // Try direct string comparison for debugging
    console.log('Are the raw strings identical?', password === hashedPassword);

    // Check hash format
    console.log('Hash format check:');
    console.log('- Starts with $2a$10$:', hashedPassword.startsWith('$2a$10$'));
    console.log('- Starts with $2b$10$:', hashedPassword.startsWith('$2b$10$'));
    console.log('- Starts with $2y$10$:', hashedPassword.startsWith('$2y$10$'));

    // Now try bcrypt compare
    console.log('Running bcrypt.compare...');
    console.log('bcrypt version:', (bcrypt as any).version || 'unknown');

    // Use bcryptjs for password comparison
    const result = await bcrypt.compare(password, hashedPassword);
    console.log('bcryptjs.compare result:', result);

    return result;
  } catch (error) {
    console.error('Error comparing passwords:', error);
    console.error('Error details:', JSON.stringify(error));
    return false;
  }
};

export const generateToken = (user: User): string => {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role,
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN } as jwt.SignOptions
  );
};

export const verifyToken = (token: string): any => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    throw new Error('Invalid token');
  }
};