# Umlamleli Loan Management Application - Distribution Package

## Overview
The Umlamleli Loan Management Application is a comprehensive web-based platform that combines a Next.js frontend with an Express.js backend, packaged into a single executable for easy distribution and deployment.

## Package Contents
- **Executable Files:**
  - `umlamleli-loan-management-win.exe` (Windows 64-bit)
  - `umlamleli-loan-management-linux` (Linux 64-bit)
  - `umlamleli-loan-management-macos` (macOS 64-bit)
- **Database Setup:**
  - `database/Umlamleli_loan_app_DB(final).sql` (PostgreSQL schema and initial data)
- **Documentation:**
  - This README file
  - User guides in the `Doc/` folder

## Prerequisites

### Required Software
1. **PostgreSQL 12 or higher**
   - Download from: https://www.postgresql.org/download/
   - Ensure PostgreSQL service is running
   - Default port 5432 should be available

2. **Available Ports**
   - Port 3000: Frontend (Next.js)
   - Port 3001: Backend API (Express.js)
   - Port 5432: PostgreSQL database

### System Requirements
- **Windows:** Windows 10/11 (64-bit)
- **Linux:** Ubuntu 18.04+ or equivalent (64-bit)
- **macOS:** macOS 10.15+ (64-bit)
- **RAM:** Minimum 4GB, Recommended 8GB
- **Storage:** 2GB free space

## Installation & Setup

### Step 1: Database Setup
1. **Install PostgreSQL** if not already installed
2. **Create Database:**
   ```sql
   createdb -U postgres umlamleli
   ```
   Or using psql:
   ```sql
   CREATE DATABASE umlamleli;
   ```

3. **Import Database Schema:**
   ```bash
   psql -U postgres -d umlamleli -f database/Umlamleli_loan_app_DB(final).sql
   ```

4. **Verify Database Setup:**
   ```sql
   psql -U postgres -d umlamleli -c "\dt"
   ```
   You should see tables like: users, loans, transactions, notifications, etc.

### Step 2: Environment Configuration
The application uses the following default database configuration:
- **Host:** localhost
- **Port:** 5432
- **Database:** umlamleli
- **Username:** postgres
- **Password:** password

**To change database credentials:**
1. The executable includes default environment variables
2. For custom database settings, you may need to set environment variables before running:
   ```bash
   # Windows (Command Prompt)
   set DB_PASSWORD=your_password
   set DB_NAME=your_database_name
   
   # Linux/macOS
   export DB_PASSWORD=your_password
   export DB_NAME=your_database_name
   ```

### Step 3: Run the Application

#### Windows
1. Double-click `umlamleli-loan-management-win.exe`
2. Or run from Command Prompt:
   ```cmd
   umlamleli-loan-management-win.exe
   ```

#### Linux
1. Make executable and run:
   ```bash
   chmod +x umlamleli-loan-management-linux
   ./umlamleli-loan-management-linux
   ```

#### macOS
1. Make executable and run:
   ```bash
   chmod +x umlamleli-loan-management-macos
   ./umlamleli-loan-management-macos
   ```

### Step 4: Access the Application
1. **Wait for startup** (approximately 10-15 seconds)
2. **Backend will start first** on http://localhost:3001
3. **Frontend will start after** on http://localhost:3000
4. **Open your browser** and navigate to: http://localhost:3000

## Default Admin Credentials
- **Email:** <EMAIL>
- **Password:** Admin123!

**⚠️ IMPORTANT:** Change the admin password immediately after first login for security.

## Application Features
- **User Management:** Registration, authentication, profile management
- **Loan Management:** Application, approval, disbursement, repayment
- **Admin Panel:** User oversight, loan processing, reporting
- **Face Recognition:** Biometric verification for enhanced security
- **Real-time Notifications:** System alerts and updates
- **Transaction Tracking:** Comprehensive payment history
- **Rate Limiting:** Built-in security with 500 requests per 15 minutes

## Troubleshooting

### Common Issues

#### 1. "Database connection failed"
- Ensure PostgreSQL is running
- Verify database credentials
- Check if database 'umlamleli' exists
- Confirm port 5432 is not blocked

#### 2. "Port already in use"
- Check if ports 3000 or 3001 are occupied:
  ```bash
  # Windows
  netstat -ano | findstr :3000
  netstat -ano | findstr :3001
  
  # Linux/macOS
  lsof -i :3000
  lsof -i :3001
  ```
- Stop conflicting processes or change ports in environment variables

#### 3. "Application won't start"
- Run from command line to see error messages
- Ensure all prerequisites are installed
- Check system requirements are met
- Verify executable permissions (Linux/macOS)

#### 4. "Frontend shows connection errors"
- Wait for backend to fully start (10-15 seconds)
- Check if backend is running on http://localhost:3001/api/health
- Verify no firewall is blocking the connections

### Performance Optimization
- **Database:** Ensure PostgreSQL has adequate memory allocation
- **System:** Close unnecessary applications to free up RAM
- **Network:** Use wired connection for better stability

## Security Considerations
- Change default admin credentials immediately
- Use strong passwords for all accounts
- Keep PostgreSQL updated and secured
- Consider running behind a reverse proxy for production use
- Regular database backups are recommended

## Support & Documentation
- **User Guide:** See `Doc/User_Guide.md`
- **Admin Guide:** See `Doc/Admin_Guide.md`
- **Technical Documentation:** See `Doc/Technical_Architecture.md`

## Version Information
- **Application Version:** 1.0.0
- **Next.js:** 15.3.2
- **Node.js Runtime:** 18.x (bundled)
- **Database:** PostgreSQL 12+

---

**Note:** This executable bundles both frontend and backend components. No separate Node.js installation is required on the target machine.
