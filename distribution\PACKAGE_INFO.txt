UMLAMLELI LOAN MANAGEMENT APPLICATION - DISTRIBUTION PACKAGE
===========================================================

Package Created: June 26, 2025
Application Version: 1.0.0
Package Type: Single Executable Distribution

CONTENTS:
---------
1. umlamleli-loan-management-win.exe    (178 MB) - Windows 64-bit executable
2. umlamleli-loan-management-linux      (186 MB) - Linux 64-bit executable  
3. umlamleli-loan-management-macos      (191 MB) - macOS 64-bit executable
4. Umlamleli_loan_app_DB(final).sql     (54 KB)  - PostgreSQL database schema
5. README.md                            (5 KB)   - Complete setup instructions

QUICK START:
-----------
1. Install PostgreSQL 12+ on target machine
2. Create database: createdb -U postgres umlamleli
3. Import schema: psql -U postgres -d umlamleli -f Umlamleli_loan_app_DB(final).sql
4. Run appropriate executable for your platform
5. Access application at http://localhost:3000

DEFAULT CREDENTIALS:
-------------------
Admin Email: <EMAIL>
Admin Password: Admin123!

TECHNICAL DETAILS:
-----------------
- Frontend: Next.js 15.3.2 (Port 3000)
- Backend: Express.js with TypeORM (Port 3001)
- Database: PostgreSQL (Port 5432)
- Runtime: Node.js 18.x (bundled)
- Rate Limiting: 500 requests per 15 minutes

PLATFORM REQUIREMENTS:
----------------------
- Windows: Windows 10/11 (64-bit)
- Linux: Ubuntu 18.04+ or equivalent (64-bit)
- macOS: macOS 10.15+ (64-bit)
- RAM: 4GB minimum, 8GB recommended
- Storage: 2GB free space

For detailed setup instructions, see README.md
