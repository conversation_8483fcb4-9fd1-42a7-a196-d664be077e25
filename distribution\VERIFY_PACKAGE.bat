@echo off
echo ============================================
echo UMLAMLELI LOAN MANAGEMENT - PACKAGE VERIFICATION
echo ============================================
echo.

echo Checking package contents...
echo.

if exist "umlamleli-loan-management-win.exe" (
    echo [✓] Windows executable found
) else (
    echo [✗] Windows executable missing
)

if exist "umlamleli-loan-management-linux" (
    echo [✓] Linux executable found
) else (
    echo [✗] Linux executable missing
)

if exist "umlamleli-loan-management-macos" (
    echo [✓] macOS executable found
) else (
    echo [✗] macOS executable missing
)

if exist "Umlamleli_loan_app_DB(final).sql" (
    echo [✓] Database schema found
) else (
    echo [✗] Database schema missing
)

if exist "README.md" (
    echo [✓] Documentation found
) else (
    echo [✗] Documentation missing
)

echo.
echo Package file sizes:
dir /s

echo.
echo ============================================
echo PACKAGE VERIFICATION COMPLETE
echo ============================================
echo.
echo To deploy:
echo 1. Install PostgreSQL 12+ on target machine
echo 2. Create database: createdb -U postgres umlamleli
echo 3. Import schema: psql -U postgres -d umlamleli -f "Umlamleli_loan_app_DB(final).sql"
echo 4. Run: umlamleli-loan-management-win.exe (Windows)
echo 5. Access: http://localhost:3000
echo.
echo Default admin: <EMAIL> / Admin123!
echo.

pause
